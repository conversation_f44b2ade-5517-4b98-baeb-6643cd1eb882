/**
 * 调试规范化问题
 */

function normalizeToJson(data) {
  let result = data;
  
  // 1. 处理对象属性名（包括嵌套的情况）
  result = result.replace(/(\s+)([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 2. 处理对象开始后的第一个属性（没有前导空白的情况）
  result = result.replace(/(\{)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 3. 处理逗号后的属性
  result = result.replace(/(,)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  return result;
}

// 改进的规范化函数
function improvedNormalizeToJson(data) {
  let result = data;
  
  // 更全面的处理，使用全局匹配
  // 匹配所有可能的属性名位置：
  // 1. 对象开始后: { property:
  // 2. 逗号后: , property:
  // 3. 换行后: \n property:
  // 4. 空白后: \s property:
  
  // 使用一个更全面的正则表达式
  result = result.replace(/([\{\s,\n]+)([a-z_$][\w$]*)\s*:/gi, function(match, prefix, propName) {
    return prefix + '"' + propName + '":';
  });
  
  return result;
}

// 测试第一个JSON对象（失败的那个）
function testFirstJsonObject() {
  console.log("=== 测试第一个JSON对象 ===");
  
  const firstJson = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29",
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西"
        },
        {
          orderId: "4500013082",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13"
        }
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed"
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0
  },
  event_type: "node_finished"
}`;

  console.log("原始JSON长度:", firstJson.length);
  
  // 测试原来的规范化函数
  console.log("\n--- 测试原来的规范化函数 ---");
  const normalized1 = normalizeToJson(firstJson);
  console.log("规范化后长度:", normalized1.length);
  
  try {
    JSON.parse(normalized1);
    console.log("✅ 原来的规范化函数成功");
  } catch (e) {
    console.log("❌ 原来的规范化函数失败:", e.message);
    
    // 找出错误位置附近的内容
    const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
    console.log("错误位置:", errorPos);
    console.log("错误位置附近的内容:", normalized1.substring(errorPos - 20, errorPos + 20));
  }
  
  // 测试改进的规范化函数
  console.log("\n--- 测试改进的规范化函数 ---");
  const normalized2 = improvedNormalizeToJson(firstJson);
  console.log("改进规范化后长度:", normalized2.length);
  
  try {
    const parsed = JSON.parse(normalized2);
    console.log("✅ 改进的规范化函数成功");
    console.log("解析出的message_id:", parsed.message.message_id);
  } catch (e) {
    console.log("❌ 改进的规范化函数失败:", e.message);
    
    // 找出错误位置附近的内容
    const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
    console.log("错误位置:", errorPos);
    console.log("错误位置附近的内容:", normalized2.substring(errorPos - 20, errorPos + 20));
  }
  
  // 显示规范化前后的差异
  console.log("\n--- 规范化前后对比（前200字符）---");
  console.log("原始:", firstJson.substring(0, 200));
  console.log("规范化:", normalized2.substring(0, 200));
}

// 测试第二个JSON对象
function testSecondJsonObject() {
  console.log("\n=== 测试第二个JSON对象 ===");
  
  const secondJson = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed"
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0
  },
  event_type: "node_finished"
}`;

  console.log("第二个JSON长度:", secondJson.length);
  
  const normalized = improvedNormalizeToJson(secondJson);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("✅ 第二个JSON解析成功");
    console.log("解析出的message_id:", parsed.message.message_id);
  } catch (e) {
    console.log("❌ 第二个JSON解析失败:", e.message);
    
    const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
    console.log("错误位置:", errorPos);
    console.log("错误位置附近的内容:", normalized.substring(errorPos - 20, errorPos + 20));
  }
}

// 运行测试
testFirstJsonObject();
testSecondJsonObject();
