/**
 * 调试JSON解析错误
 */

// 最终修复的规范化函数
function normalizeToJson(data) {
  let result = data;
  
  // 处理所有可能的属性名情况，包含换行符
  result = result.replace(
    /([{\s,\n\r]+)([a-z_$][\w$]*)\s*:/gi,
    function(match, prefix, propName) {
      return prefix + '"' + propName + '":';
    }
  );
  
  return result;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

// 调试JSON解析错误
function debugJsonError() {
  console.log("=== 调试JSON解析错误 ===");
  
  // 你的精确数据
  const exactData = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236", 
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",          prodStartDate: "2022/09/29", 
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西", 
        },
        {
          orderId: "4500013082", 
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",     shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}`;

  // 提取第一个JSON对象
  const firstResult = extractCompleteJsonFromPosition(exactData, 0);
  
  if (firstResult.jsonStr) {
    console.log("第一个JSON提取成功，长度:", firstResult.jsonStr.length);
    
    // 规范化
    const normalized = normalizeToJson(firstResult.jsonStr);
    console.log("规范化后长度:", normalized.length);
    
    try {
      const parsed = JSON.parse(normalized);
      console.log("✅ JSON解析成功");
    } catch (e) {
      console.log("❌ JSON解析失败:", e.message);
      
      // 获取错误位置
      const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
      console.log("错误位置:", errorPos);
      
      // 显示错误位置附近的详细内容
      console.log("\n错误位置附近的详细内容:");
      for (let i = Math.max(0, errorPos - 20); i < Math.min(normalized.length, errorPos + 20); i++) {
        const char = normalized[i];
        const marker = i === errorPos ? " <-- ERROR" : "";
        const charCode = char.charCodeAt(0);
        const displayChar = char === '\n' ? '\\n' : char === '\r' ? '\\r' : char === '\t' ? '\\t' : char;
        console.log(`${i}: "${displayChar}" (${charCode})${marker}`);
      }
      
      // 尝试手动修复
      console.log("\n尝试手动修复...");
      
      // 查找所有可能的问题
      const lines = normalized.split('\n');
      let hasIssue = false;
      
      lines.forEach((line, index) => {
        // 查找没有引号的属性名
        const unquotedMatch = line.match(/([^"]\s*)([a-z_$][\w$]*)\s*:/i);
        if (unquotedMatch) {
          console.log(`第${index + 1}行有问题: "${line.trim()}"`);
          console.log(`匹配到: "${unquotedMatch[0]}"`);
          hasIssue = true;
        }
      });
      
      if (!hasIssue) {
        console.log("没有找到明显的属性名问题");
        
        // 检查是否有其他JSON语法问题
        console.log("\n检查其他可能的问题:");
        
        // 检查是否有多余的逗号
        if (normalized.includes(',}') || normalized.includes(',]')) {
          console.log("发现多余的逗号");
        }
        
        // 检查是否有缺失的引号
        const quoteCount = (normalized.match(/"/g) || []).length;
        console.log("引号数量:", quoteCount, quoteCount % 2 === 0 ? "(偶数，正常)" : "(奇数，可能有问题)");
        
        // 保存规范化后的JSON到文件以便检查
        console.log("\n保存规范化后的JSON前500字符:");
        console.log(normalized.substring(0, 500));
        
        console.log("\n保存规范化后的JSON错误位置附近:");
        console.log(normalized.substring(Math.max(0, errorPos - 100), errorPos + 100));
      }
    }
  }
}

// 运行调试
debugJsonError();
