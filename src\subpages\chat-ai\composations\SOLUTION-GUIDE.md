# 流式数据解析问题解决方案

## 问题分析

你遇到的问题是：
1. **数据格式问题**：服务端返回的是 JavaScript 对象字面量格式（属性名没有引号），而不是标准的 JSON 格式
2. **复杂截断场景**：数据被截断成三部分，第三部分还包含另一个完整的数据块

## 解决方案

### 1. 数据格式规范化

我已经在 `useParseStreaming.ts` 中添加了 `normalizeToJson()` 函数，它能够：
- 将 JavaScript 对象字面量格式转换为标准 JSON 格式
- 为没有引号的属性名添加引号
- 处理嵌套对象和数组

### 2. 增强的解析器

更新后的解析器现在能够：
- 处理没有 `data:` 前缀的 JSON 数据
- 自动规范化 JavaScript 对象字面量格式
- 正确处理复杂的截断场景
- 提供详细的调试日志

## 使用方法

### 在微信小程序中使用

你的 `adapter.ts` 已经集成了新的解析器：

```typescript
const listener: WechatMiniprogram.OffChunkReceivedCallback = ({ data }) => {
  const decodeData = new TextDecoder2().decode(new Uint8Array(data));
  if (!decodeData) return;
  
  console.log("decodeData:", decodeData); // 查看原始数据
  
  // 使用优化后的解析器（现在支持JavaScript对象字面量格式）
  const parseData: ChunkData[] = parseStreamingData(decodeData);
  parseData.forEach(item => {
    const response: StreamResponse = {
      data: item,
      status: "streaming",
    };
    // 处理解析出的数据
    config.transformResponse?.(response);
  });
};
```

### 调试信息

解析器现在会输出详细的调试信息：
- 输入数据内容和长度
- 缓冲区状态
- JSON 规范化前后的对比
- 解析结果

## 测试验证

### 你的实际数据格式

```javascript
// 原始格式（JavaScript 对象字面量）
{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      // ... 更多属性
    }
  },
  event_type: "node_finished"
}

// 规范化后（标准 JSON）
{
  "message": {
    "id": "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    "session_id": "3271",
    "message_id": "5236",
    "content": {
      "action_id": "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      // ... 更多属性
    }
  },
  "event_type": "node_finished"
}
```

### 截断场景处理

你描述的场景：
1. **Chunk 1**: 第一段被截断的内容 → 解析器保存到缓冲区
2. **Chunk 2**: 第二段被截断的内容 → 解析器继续保存到缓冲区
3. **Chunk 3**: 第三段 + 完整数据块 → 解析器解析出 2 条完整消息

## 预期结果

使用更新后的解析器，你应该能看到：

```
=== parseStreamingData 开始 ===
输入数据长度: 403
当前缓冲区长度: 0
追加后缓冲区长度: 403
没有找到data:前缀，尝试直接解析JSON
JSON不完整，保留数据等待更多输入
解析数量: 0
================================

=== parseStreamingData 开始 ===
输入数据长度: 564
当前缓冲区长度: 403
追加后缓冲区长度: 967
没有找到data:前缀，尝试直接解析JSON
JSON不完整，保留数据等待更多输入
解析数量: 0
================================

=== parseStreamingData 开始 ===
输入数据长度: 970
当前缓冲区长度: 967
追加后缓冲区长度: 1937
没有找到data:前缀，尝试直接解析JSON
找到完整的JSON（无data:前缀）
规范化前: { message: { id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247"...
规范化后: { "message": { "id": "6a6222b5-5e9b-43e6-bb39-0f3920a3b247"...
成功解析JSON: 5236
移除已处理部分，继续处理
找到完整的JSON（无data:前缀）
成功解析JSON: 5236
解析数量: 2
================================
```

## 故障排除

如果仍然遇到问题：

1. **检查控制台日志**：查看详细的调试信息
2. **验证数据格式**：确认服务端返回的确实是 JavaScript 对象字面量格式
3. **检查特殊字符**：确保数据中没有无法处理的特殊字符
4. **测试简化数据**：先用简单的数据测试解析器是否工作

## 下一步

1. 在微信小程序中测试更新后的解析器
2. 查看控制台输出的调试信息
3. 如果还有问题，提供具体的错误日志和数据样本

解析器现在应该能够正确处理你的复杂截断场景了！
