import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 流式解析器状态管理
 */
interface StreamParserState {
  buffer: string;
  lastProcessedIndex: number;
  isStreamEnded: boolean;
}

// 全局状态，用于存储解析状态
let parserState: StreamParserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

/**
 * 优化的流式数据解析器
 * 支持复杂的截断情况：
 * 1. 单个JSON对象被分割成多个chunk
 * 2. 一个chunk包含多个完整和不完整的data块
 * 3. data:前缀本身被截断
 * 4. JSON对象在任意位置被截断
 *
 * @param data - 新接收到的数据块
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  // 将新数据追加到缓存中 - 这是拼接的关键
  parserState.buffer += data;

  const parsedData: ChunkData[] = [];
  let processedLength = 0;

  // 持续处理缓冲区中的数据
  while (processedLength < parserState.buffer.length) {
    // 从当前位置查找data:
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);

    if (dataIndex === -1) {
      // 没有找到data:，检查剩余数据
      const remaining = parserState.buffer.substring(processedLength);
      const trimmed = remaining.trim();

      // 检查是否是不完整的data:前缀
      if (
        trimmed.length > 0 &&
        trimmed.length < 5 &&
        "data:".startsWith(trimmed)
      ) {
        // 保留不完整的data:前缀
        parserState.buffer = trimmed;
      } else {
        // 没有有效数据，清空缓冲区
        parserState.buffer = "";
      }
      break;
    }

    // 找到data:，查找JSON开始位置
    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);

    if (jsonStart === -1) {
      // 没有找到JSON开始标记
      const remainingLength = parserState.buffer.length - dataIndex;
      if (remainingLength < 20) {
        // 数据太短，可能不完整，保留等待更多数据
        parserState.buffer = parserState.buffer.substring(dataIndex);
        break;
      } else {
        // 跳过这个无效的data:
        processedLength = dataIndex + 5;
        continue;
      }
    }

    // 尝试提取完整的JSON
    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );

    if (jsonResult.jsonStr) {
      // 找到完整的JSON，尝试解析
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed as ChunkData);

          // 成功解析，移除已处理的部分
          const endIndex = jsonStart + jsonResult.jsonStr.length;
          parserState.buffer = parserState.buffer.substring(endIndex);
          processedLength = 0; // 重置处理位置
          continue;
        }
      } catch {
        // JSON解析失败，跳过这个数据块
        processedLength = jsonStart + jsonResult.jsonStr.length;
        continue;
      }
    } else {
      // JSON不完整，保留从dataIndex开始的所有数据
      parserState.buffer = parserState.buffer.substring(dataIndex);
      break;
    }
  }
  console.log("=== parseStreamingData Debug ===");
  console.log("输入数据:", JSON.stringify(data));
  console.log("最终缓冲区:", JSON.stringify(parserState.buffer));
  console.log("parsedData:", parsedData);
  console.log("解析数量:", parsedData.length);
  console.log("================================");

  return parsedData;
}

/**
 * 查找JSON开始位置
 */
function findJsonStart(buffer: string, startIndex: number): number {
  let index = startIndex;

  // 跳过空白字符
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }

  // 检查是否找到JSON开始标记
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }

  return -1;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isComplete: boolean;
} {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

/**
 * 批量处理多个数据块
 * 适用于一次性接收到多个chunk的情况
 *
 * @param chunks - 数据块数组
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingDataBatch(chunks: string[]): ChunkData[] {
  const allParsedData: ChunkData[] = [];

  for (let i = 0; i < chunks.length; i++) {
    const parsedData = parseStreamingData(chunks[i]);
    allParsedData.push(...parsedData);
  }

  return allParsedData;
}

/**
 * 获取解析器当前状态信息
 * 用于调试和监控
 */
export function getParserState(): Readonly<StreamParserState> {
  return { ...parserState };
}

/**
 * 检查缓冲区是否有未处理的数据
 */
export function hasUnprocessedData(): boolean {
  return parserState.buffer.length > 0;
}

/**
 * 重置解析器的内部状态
 */
export function resetStreamingParser(): void {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

/**
 * 刷新剩余数据
 * 在流结束时调用，尝试解析缓冲区中剩余的数据
 * 这对于处理最后一个可能不完整的数据块很重要
 */
export function flushRemainingData(): ChunkData[] {
  const parsedData: ChunkData[] = [];

  if (parserState.buffer.trim().length === 0) {
    return parsedData;
  }

  // 标记流已结束，这样可以更宽松地处理剩余数据
  parserState.isStreamEnded = true;

  // 尝试解析缓冲区中的剩余数据
  let processedLength = 0;

  while (processedLength < parserState.buffer.length) {
    // 查找data:
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);

    if (dataIndex === -1) {
      // 没有找到data:，尝试直接解析JSON（可能缺少data:前缀）
      const remaining = parserState.buffer.substring(processedLength).trim();
      if (remaining.startsWith("{")) {
        try {
          const parsed = JSON.parse(remaining);
          if (parsed && typeof parsed === "object") {
            parsedData.push(parsed as ChunkData);
          }
        } catch {
          // 解析失败，忽略
        }
      }
      break;
    }

    // 找到data:，查找JSON开始位置
    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);

    if (jsonStart === -1) {
      processedLength = dataIndex + 5;
      continue;
    }

    // 尝试提取JSON，在流结束时更宽松地处理
    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );

    if (jsonResult.jsonStr) {
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed as ChunkData);
        }
      } catch {
        // 解析失败，忽略
      }
      processedLength = jsonStart + jsonResult.jsonStr.length;
    } else {
      // 如果流已结束，尝试解析剩余的不完整JSON
      const remaining = parserState.buffer.substring(jsonStart);
      if (remaining.trim().length > 0) {
        try {
          // 尝试修复不完整的JSON（添加缺失的括号等）
          const fixedJson = attemptJsonFix(remaining);
          if (fixedJson) {
            const parsed = JSON.parse(fixedJson);
            if (parsed && typeof parsed === "object") {
              parsedData.push(parsed as ChunkData);
            }
          }
        } catch {
          // 修复失败，忽略
        }
      }
      break;
    }
  }

  // 清空缓冲区
  parserState.buffer = "";

  return parsedData;
}

/**
 * 尝试修复不完整的JSON字符串
 * 这是一个简单的修复尝试，主要处理缺失的结束括号
 */
function attemptJsonFix(jsonStr: string): string | null {
  const trimmed = jsonStr.trim();
  if (!trimmed.startsWith("{")) {
    return null;
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;
  let lastValidIndex = -1;

  for (let i = 0; i < trimmed.length; i++) {
    const char = trimmed[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return trimmed.substring(0, i + 1);
        }
      }
    }

    // 记录最后一个有效字符位置
    if (!inString && /[a-z0-9"}\]]/i.test(char)) {
      lastValidIndex = i;
    }
  }

  // 如果括号不匹配，尝试添加缺失的结束括号
  if (braceCount > 0 && lastValidIndex > -1) {
    let fixed = trimmed.substring(0, lastValidIndex + 1);

    // 如果最后一个字符不是引号、括号或数字，可能需要添加引号
    const lastChar = fixed[fixed.length - 1];
    if (inString && lastChar !== '"') {
      fixed += '"';
    }

    // 添加缺失的结束括号
    fixed += "}".repeat(braceCount);

    return fixed;
  }

  return null;
}
