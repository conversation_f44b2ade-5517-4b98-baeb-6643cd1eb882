/**
 * 多消息解析测试
 * 测试解析器处理多个连续消息的能力
 */

// 模拟解析器核心逻辑
let parserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

function resetStreamingParser() {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

function findJsonStart(buffer, startIndex) {
  let index = startIndex;
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }
  return -1;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

function parseStreamingData(data) {
  parserState.buffer += data;
  const parsedData = [];
  let processedLength = 0;

  while (processedLength < parserState.buffer.length) {
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);

    if (dataIndex === -1) {
      const remaining = parserState.buffer.substring(processedLength);
      const trimmed = remaining.trim();

      if (
        trimmed.length > 0 &&
        trimmed.length < 5 &&
        "data:".startsWith(trimmed)
      ) {
        parserState.buffer = trimmed;
      } else {
        parserState.buffer = "";
      }
      break;
    }

    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);

    if (jsonStart === -1) {
      const remainingLength = parserState.buffer.length - dataIndex;
      if (remainingLength < 20) {
        parserState.buffer = parserState.buffer.substring(dataIndex);
        break;
      } else {
        processedLength = dataIndex + 5;
        continue;
      }
    }

    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );

    if (jsonResult.jsonStr) {
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed);
          const endIndex = jsonStart + jsonResult.jsonStr.length;
          parserState.buffer = parserState.buffer.substring(endIndex);
          processedLength = 0;
          continue;
        }
      } catch {
        processedLength = jsonStart + jsonResult.jsonStr.length;
        continue;
      }
    } else {
      parserState.buffer = parserState.buffer.substring(dataIndex);
      break;
    }
  }

  return parsedData;
}

// 测试多个连续消息
function testMultipleMessages() {
  console.log("=== 多消息解析测试 ===");
  resetStreamingParser();

  // 创建包含多个完整消息的数据
  const multipleMessages = `data: {"message": {"content": "第一条消息", "message_id": "msg1"}, "event_type": "message"}
data: {"message": {"content": "第二条消息", "message_id": "msg2"}, "event_type": "message"}
data: {"message": {"content": "第三条消息", "message_id": "msg3"}, "event_type": "message"}`;

  console.log("输入数据:");
  console.log(multipleMessages);
  console.log("\n开始解析...");

  const results = parseStreamingData(multipleMessages);
  
  console.log(`解析出 ${results.length} 条消息`);
  console.log(`缓冲区剩余长度: ${parserState.buffer.length}`);

  results.forEach((result, index) => {
    console.log(`消息 ${index + 1}: ${result.message.content} (ID: ${result.message.message_id})`);
  });

  if (results.length === 3) {
    console.log("✅ 多消息解析测试通过");
  } else {
    console.log("❌ 多消息解析测试失败");
  }
}

// 测试截断的多消息场景
function testTruncatedMultipleMessages() {
  console.log("\n=== 截断多消息测试 ===");
  resetStreamingParser();

  // 第一个chunk：包含完整的第一条消息和第二条消息的一部分
  const chunk1 = `data: {"message": {"content": "完整的第一条消息", "message_id": "msg1"}, "event_type": "message"}
data: {"message": {"content": "被截断的第二条`;

  // 第二个chunk：第二条消息的剩余部分和第三条消息
  const chunk2 = `消息内容", "message_id": "msg2"}, "event_type": "message"}
data: {"message": {"content": "第三条完整消息", "message_id": "msg3"}, "event_type": "message"}`;

  console.log("Chunk 1:");
  console.log(chunk1);
  console.log("\nChunk 2:");
  console.log(chunk2);

  console.log("\n处理第一个chunk...");
  const results1 = parseStreamingData(chunk1);
  console.log(`解析出 ${results1.length} 条消息`);
  console.log(`缓冲区剩余长度: ${parserState.buffer.length}`);

  console.log("\n处理第二个chunk...");
  const results2 = parseStreamingData(chunk2);
  console.log(`解析出 ${results2.length} 条消息`);
  console.log(`缓冲区剩余长度: ${parserState.buffer.length}`);

  const totalResults = results1.length + results2.length;
  console.log(`\n总共解析出 ${totalResults} 条消息`);

  // 显示所有解析的消息
  const allResults = [...results1, ...results2];
  allResults.forEach((result, index) => {
    console.log(`消息 ${index + 1}: ${result.message.content} (ID: ${result.message.message_id})`);
  });

  if (totalResults === 3) {
    console.log("✅ 截断多消息测试通过");
  } else {
    console.log("❌ 截断多消息测试失败");
  }
}

// 测试复杂的混合场景
function testComplexMixedScenario() {
  console.log("\n=== 复杂混合场景测试 ===");
  resetStreamingParser();

  // 模拟真实的复杂数据流
  const chunks = [
    // Chunk 1: 完整消息 + 不完整消息开始
    'data: {"message": {"content": "用户提问", "message_id": "q1"}, "event_type": "message"}\ndata: {"message": {"content": "AI开始回答：人工智能是一个',
    
    // Chunk 2: 继续不完整消息 + 新的完整消息
    '复杂的技术领域", "message_id": "a1"}, "event_type": "message"}\ndata: {"message": {"content": "补充说明", "message_id": "a2"}, "event_type": "message"}',
    
    // Chunk 3: 最后一个不完整消息
    '\ndata: {"message": {"content": "最后的总结',
    
    // Chunk 4: 完成最后的消息
    '内容", "message_id": "a3"}, "event_type": "message"}'
  ];

  let totalParsed = 0;
  let allResults = [];

  chunks.forEach((chunk, index) => {
    console.log(`\n处理 Chunk ${index + 1}:`);
    console.log(`内容: ${chunk.substring(0, 60)}...`);
    
    const results = parseStreamingData(chunk);
    totalParsed += results.length;
    allResults.push(...results);
    
    console.log(`解析出 ${results.length} 条消息`);
    console.log(`缓冲区剩余: ${parserState.buffer.length} 字符`);
  });

  console.log(`\n总共解析出 ${totalParsed} 条消息:`);
  allResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.message.content} (${result.message.message_id})`);
  });

  if (totalParsed === 4) {
    console.log("✅ 复杂混合场景测试通过");
  } else {
    console.log("❌ 复杂混合场景测试失败");
  }
}

// 运行所有测试
testMultipleMessages();
testTruncatedMultipleMessages();
testComplexMixedScenario();

console.log("\n=== 所有测试完成 ===");
