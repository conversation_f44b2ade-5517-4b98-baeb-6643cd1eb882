/**
 * 边缘情况测试
 * 测试更多复杂的截断场景
 */

// 复制解析器核心逻辑
let parserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

function resetStreamingParser() {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

function findJsonStart(buffer, startIndex) {
  let index = startIndex;
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }
  return -1;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

function parseStreamingData(data) {
  parserState.buffer += data;
  const parsedData = [];
  let processedLength = 0;

  while (processedLength < parserState.buffer.length) {
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);

    if (dataIndex === -1) {
      const remaining = parserState.buffer.substring(processedLength);
      const trimmed = remaining.trim();

      if (
        trimmed.length > 0 &&
        trimmed.length < 5 &&
        "data:".startsWith(trimmed)
      ) {
        parserState.buffer = trimmed;
      } else {
        parserState.buffer = "";
      }
      break;
    }

    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);

    if (jsonStart === -1) {
      const remainingLength = parserState.buffer.length - dataIndex;
      if (remainingLength < 20) {
        parserState.buffer = parserState.buffer.substring(dataIndex);
        break;
      } else {
        processedLength = dataIndex + 5;
        continue;
      }
    }

    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );

    if (jsonResult.jsonStr) {
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed);
          const endIndex = jsonStart + jsonResult.jsonStr.length;
          parserState.buffer = parserState.buffer.substring(endIndex);
          processedLength = 0;
          continue;
        }
      } catch {
        processedLength = jsonStart + jsonResult.jsonStr.length;
        continue;
      }
    } else {
      parserState.buffer = parserState.buffer.substring(dataIndex);
      break;
    }
  }

  return parsedData;
}

// 测试用例

function testMultipleDataBlocksWithTruncation() {
  console.log("\n1. 测试多个data块混合截断:");
  resetStreamingParser();

  // 第一个完整块 + 第二个块的开始
  const chunk1 = 'data: {"message": {"content": "完整消息1", "message_id": "msg1"}, "event_type": "message"}\ndata: {"message": {"content": "截断消息';
  
  // 第二个块的中间部分
  const chunk2 = '2的内容", "message_id": "msg2"}, "event_type": "message"}\ndata: {"message": {"content": "第三个';
  
  // 第二个块的结束 + 第三个块的完整部分
  const chunk3 = '消息", "message_id": "msg3"}, "event_type": "message"}';

  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);
  console.log("Chunk3:", chunk3);

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后 - 解析数量:", result2.length);

  const result3 = parseStreamingData(chunk3);
  console.log("处理chunk3后 - 解析数量:", result3.length);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 3) {
    console.log("✅ 多个data块混合截断测试通过");
  } else {
    console.log("❌ 多个data块混合截断测试失败");
  }
}

function testJsonWithEscapedQuotes() {
  console.log("\n2. 测试包含转义引号的JSON截断:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "这是一个包含\\"转义引号\\"的';
  const part2 = '消息内容", "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 转义引号截断测试通过");
    console.log("解析结果:", result2[0]);
  } else {
    console.log("❌ 转义引号截断测试失败");
  }
}

function testNestedObjectTruncation() {
  console.log("\n3. 测试嵌套对象截断:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "消息", "nested": {"level1": {"level2": {"deep';
  const part2 = 'Value": "深层数据"}, "other": "其他"}, "back": "返回"}, "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 嵌套对象截断测试通过");
    console.log("解析结果:", JSON.stringify(result2[0], null, 2));
  } else {
    console.log("❌ 嵌套对象截断测试失败");
  }
}

function testVerySmallChunks() {
  console.log("\n4. 测试极小数据块:");
  resetStreamingParser();

  const fullData = 'data: {"message": {"content": "测试", "message_id": "msg1"}, "event_type": "message"}';
  
  // 将数据分成单字符块
  const chunks = fullData.split('');
  
  console.log("将数据分成", chunks.length, "个单字符块");
  
  let totalResults = 0;
  for (let i = 0; i < chunks.length; i++) {
    const results = parseStreamingData(chunks[i]);
    totalResults += results.length;
    if (results.length > 0) {
      console.log("在第", i + 1, "个字符后解析出数据");
    }
  }

  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 极小数据块测试通过");
  } else {
    console.log("❌ 极小数据块测试失败");
  }
}

function testIncompleteDataPrefix() {
  console.log("\n5. 测试不完整的data前缀:");
  resetStreamingParser();

  const chunks = ['d', 'a', 't', 'a', ':', ' ', '{"message": {"content": "测试", "message_id": "msg1"}, "event_type": "message"}'];
  
  let totalResults = 0;
  for (let i = 0; i < chunks.length; i++) {
    const results = parseStreamingData(chunks[i]);
    totalResults += results.length;
    console.log(`处理 "${chunks[i]}" 后 - 解析数量:`, results.length, "缓冲区:", parserState.buffer.substring(0, 20) + "...");
  }

  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 不完整data前缀测试通过");
  } else {
    console.log("❌ 不完整data前缀测试失败");
  }
}

// 运行所有测试
console.log("=== 边缘情况测试 ===");
testMultipleDataBlocksWithTruncation();
testJsonWithEscapedQuotes();
testNestedObjectTruncation();
testVerySmallChunks();
testIncompleteDataPrefix();
console.log("\n=== 边缘情况测试完成 ===");
