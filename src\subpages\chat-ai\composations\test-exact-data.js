/**
 * 测试你的精确数据
 */

// 修复后的规范化函数
function normalizeToJson(data) {
  let result = data;
  
  // 一次性处理所有情况
  result = result.replace(
    /([{\s,\n]+)([a-z_$][\w$]*)\s*:/gi,
    function(match, prefix, propName) {
      return prefix + '"' + propName + '":';
    }
  );
  
  return result;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

// 测试你的精确数据（从你的chunk组合而来）
function testExactData() {
  console.log("=== 测试你的精确数据 ===");
  
  // 这是你的chunk1 + chunk2 + chunk3的精确组合
  const exactData = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236", 
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",          prodStartDate: "2022/09/29", 
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西", 
        },
        {
          orderId: "4500013082", 
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",     shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}

{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1", 
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}`;

  console.log("精确数据长度:", exactData.length);
  
  // 提取第一个JSON对象
  console.log("\n--- 提取第一个JSON对象 ---");
  const firstResult = extractCompleteJsonFromPosition(exactData, 0);
  
  if (firstResult.jsonStr) {
    console.log("第一个JSON提取成功，长度:", firstResult.jsonStr.length);
    
    // 检查提取的JSON的结尾
    console.log("提取的JSON结尾100字符:", firstResult.jsonStr.substring(firstResult.jsonStr.length - 100));
    
    // 规范化并解析
    const normalized1 = normalizeToJson(firstResult.jsonStr);
    console.log("规范化后长度:", normalized1.length);
    
    try {
      const parsed1 = JSON.parse(normalized1);
      console.log("✅ 第一个JSON解析成功:", parsed1.message.message_id);
    } catch (e) {
      console.log("❌ 第一个JSON解析失败:", e.message);
      
      // 显示错误位置附近的内容
      const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
      console.log("错误位置:", errorPos);
      console.log("错误位置附近的内容:", normalized1.substring(errorPos - 50, errorPos + 50));
      
      // 检查是否有格式问题
      console.log("\n检查原始JSON在错误位置附近:");
      console.log("原始JSON错误位置附近:", firstResult.jsonStr.substring(errorPos - 50, errorPos + 50));
    }
    
    // 查找第二个JSON对象
    const remainingData = exactData.substring(firstResult.jsonStr.length).trim();
    console.log("\n--- 提取第二个JSON对象 ---");
    console.log("剩余数据长度:", remainingData.length);
    console.log("剩余数据前50字符:", remainingData.substring(0, 50));
    
    if (remainingData.startsWith('{')) {
      const secondResult = extractCompleteJsonFromPosition(remainingData, 0);
      
      if (secondResult.jsonStr) {
        console.log("第二个JSON提取成功，长度:", secondResult.jsonStr.length);
        
        const normalized2 = normalizeToJson(secondResult.jsonStr);
        
        try {
          const parsed2 = JSON.parse(normalized2);
          console.log("✅ 第二个JSON解析成功:", parsed2.message.message_id);
          
          console.log("\n🎉 总共成功解析了2个JSON对象！");
        } catch (e) {
          console.log("❌ 第二个JSON解析失败:", e.message);
        }
      } else {
        console.log("❌ 第二个JSON提取失败");
      }
    } else {
      console.log("❌ 剩余数据不是有效的JSON开始");
    }
    
  } else {
    console.log("❌ 第一个JSON提取失败");
  }
}

// 运行测试
testExactData();
