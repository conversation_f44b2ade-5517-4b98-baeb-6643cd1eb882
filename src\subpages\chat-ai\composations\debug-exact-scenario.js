/**
 * 精确模拟你的使用场景
 */

// 完全复制解析器的逻辑
let parserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

function resetStreamingParser() {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

function normalizeToJson(data) {
  let result = data;
  
  // 1. 处理对象属性名（包括嵌套的情况）
  result = result.replace(/(\s+)([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 2. 处理对象开始后的第一个属性（没有前导空白的情况）
  result = result.replace(/(\{)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 3. 处理逗号后的属性
  result = result.replace(/(,)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  return result;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

function parseStreamingData(data) {
  console.log("=== parseStreamingData 开始 ===");
  console.log("输入数据长度:", data.length);
  console.log("输入数据内容:", JSON.stringify(data.substring(0, 100)) + (data.length > 100 ? "..." : ""));
  console.log("当前缓冲区长度:", parserState.buffer.length);
  console.log("当前缓冲区内容:", JSON.stringify(parserState.buffer.substring(0, 100)) + (parserState.buffer.length > 100 ? "..." : ""));
  
  // 将新数据追加到缓存中
  parserState.buffer += data;
  
  console.log("追加后缓冲区长度:", parserState.buffer.length);
  console.log("追加后缓冲区内容:", JSON.stringify(parserState.buffer.substring(0, 100)) + (parserState.buffer.length > 100 ? "..." : ""));

  const parsedData = [];
  let processedLength = 0;

  // 持续处理缓冲区中的数据
  while (processedLength < parserState.buffer.length) {
    console.log(`处理位置: ${processedLength}, 缓冲区长度: ${parserState.buffer.length}`);
    
    // 首先尝试查找data:前缀
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);
    
    // 如果没有找到data:前缀，尝试直接解析JSON（处理没有data:前缀的情况）
    if (dataIndex === -1) {
      console.log("没有找到data:前缀，尝试直接解析JSON");

      // 查找JSON开始位置
      let jsonStart = -1;
      for (let i = processedLength; i < parserState.buffer.length; i++) {
        const char = parserState.buffer[i];
        if (char === "{") {
          jsonStart = i;
          break;
        } else if (!/\s/.test(char)) {
          // 遇到非空白字符但不是{，跳过这个字符
          processedLength = i + 1;
          break;
        }
      }
      
      if (jsonStart === -1) {
        // 没有找到JSON开始标记，检查剩余数据
        const remaining = parserState.buffer.substring(processedLength);
        const trimmed = remaining.trim();
        console.log("没有找到JSON开始标记，剩余数据:", JSON.stringify(trimmed.substring(0, 50)));

        // 检查是否是不完整的data:前缀
        if (
          trimmed.length > 0 &&
          trimmed.length < 5 &&
          "data:".startsWith(trimmed)
        ) {
          // 保留不完整的data:前缀
          parserState.buffer = trimmed;
          console.log("保留不完整的data:前缀");
        } else if (trimmed.length > 0 && trimmed.length < 20) {
          // 数据太短，可能是不完整的JSON开始，保留等待更多数据
          parserState.buffer = trimmed;
          console.log("保留可能不完整的数据");
        } else {
          // 没有有效数据，清空缓冲区
          parserState.buffer = "";
          console.log("清空缓冲区");
        }
        break;
      }
      
      console.log(`找到JSON开始位置: ${jsonStart}`);
      
      // 找到JSON开始位置，尝试提取完整的JSON
      const jsonResult = extractCompleteJsonFromPosition(
        parserState.buffer,
        jsonStart,
      );
      
      if (jsonResult.jsonStr) {
        console.log("找到完整的JSON（无data:前缀）");
        console.log("JSON长度:", jsonResult.jsonStr.length);
        console.log("JSON前100字符:", jsonResult.jsonStr.substring(0, 100));
        
        try {
          // 规范化JSON格式（处理JavaScript对象字面量）
          const normalizedJson = normalizeToJson(jsonResult.jsonStr);
          console.log("规范化前:", jsonResult.jsonStr.substring(0, 100) + "...");
          console.log("规范化后:", normalizedJson.substring(0, 100) + "...");
          
          const parsed = JSON.parse(normalizedJson);
          if (parsed && typeof parsed === "object") {
            parsedData.push(parsed);
            console.log("成功解析JSON:", parsed.message?.message_id || parsed.message?.id || "unknown");

            // 成功解析，移除已处理的部分
            const endIndex = jsonStart + jsonResult.jsonStr.length;
            parserState.buffer = parserState.buffer.substring(endIndex);
            console.log("移除已处理部分，继续处理");
            console.log("新缓冲区长度:", parserState.buffer.length);
            console.log("新缓冲区内容:", JSON.stringify(parserState.buffer.substring(0, 50)));
            processedLength = 0; // 重置处理位置
            continue;
          }
        } catch (e) {
          console.log("JSON解析失败:", e instanceof Error ? e.message : String(e));
          console.log("失败的JSON:", jsonResult.jsonStr.substring(0, 200));
          processedLength = jsonStart + jsonResult.jsonStr.length;
          continue;
        }
      } else {
        // JSON不完整，保留从jsonStart开始的所有数据
        parserState.buffer = parserState.buffer.substring(jsonStart);
        console.log("JSON不完整，保留数据等待更多输入");
        console.log("保留的数据长度:", parserState.buffer.length);
        break;
      }
    } else {
      console.log("找到data:前缀，使用标准SSE解析");
      // 这里是原来的data:前缀处理逻辑
      break;
    }
  }

  console.log("parsedData:", parsedData);
  console.log("解析数量:", parsedData.length);
  console.log("最终缓冲区:", JSON.stringify(parserState.buffer.substring(0, 50)));
  console.log("================================");
  
  return parsedData;
}

// 测试你的精确场景
function testExactScenario() {
  console.log("=== 测试精确场景 ===");
  resetStreamingParser();

  // 你的实际数据
  const chunk1 = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236", 
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",`;

  const chunk2 = `          prodStartDate: "2022/09/29", 
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西", 
        },
        {
          orderId: "4500013082", 
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",`;

  const chunk3 = `     shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}

{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1", 
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}`;

  console.log("Chunk 1 长度:", chunk1.length);
  console.log("Chunk 2 长度:", chunk2.length);
  console.log("Chunk 3 长度:", chunk3.length);

  // 第一次流式输出
  console.log("\n>>> 处理第一次流式输出");
  const result1 = parseStreamingData(chunk1);
  console.log(`第一次结果: ${result1.length} 条消息\n`);

  // 第二次流式输出
  console.log(">>> 处理第二次流式输出");
  const result2 = parseStreamingData(chunk2);
  console.log(`第二次结果: ${result2.length} 条消息\n`);

  // 第三次流式输出
  console.log(">>> 处理第三次流式输出");
  const result3 = parseStreamingData(chunk3);
  console.log(`第三次结果: ${result3.length} 条消息\n`);

  const totalResults = result1.length + result2.length + result3.length;
  console.log(`总共解析出 ${totalResults} 条消息`);

  if (totalResults === 2) {
    console.log("✅ 测试通过");
  } else {
    console.log("❌ 测试失败");
  }
}

// 运行测试
testExactScenario();
