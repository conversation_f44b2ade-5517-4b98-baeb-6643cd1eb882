/**
 * 测试修复后的解析功能
 */

import { parseStreamingData, resetStreamingParser, hasUnprocessedData, getParserState } from "./useParseStreaming";

console.log("=== 测试修复后的解析功能 ===");

/**
 * 测试1: 基本完整数据解析
 */
function testCompleteData() {
  console.log("\n1. 测试完整数据解析:");
  resetStreamingParser();

  const testData = 'data: {"message": {"content": "Hello World", "message_id": "msg1"}, "event_type": "message"}';
  console.log("输入数据:", testData);

  const results = parseStreamingData(testData);
  console.log("解析结果数量:", results.length);
  console.log("解析结果:", results);
  console.log("缓冲区状态:", getParserState().buffer);

  if (results.length === 1) {
    console.log("✅ 完整数据解析测试通过");
  } else {
    console.log("❌ 完整数据解析测试失败");
  }
}

/**
 * 测试2: 截断数据拼接
 */
function testTruncatedData() {
  console.log("\n2. 测试截断数据拼接:");
  resetStreamingParser();

  // 分割数据
  const part1 = 'data: {"message": {"content": "Hello';
  const part2 = ' World", "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  // 处理第一部分
  const result1 = parseStreamingData(part1);
  console.log("处理part1后:");
  console.log("  解析数量:", result1.length);
  console.log("  缓冲区:", getParserState().buffer);

  // 处理第二部分
  const result2 = parseStreamingData(part2);
  console.log("处理part2后:");
  console.log("  解析数量:", result2.length);
  console.log("  缓冲区:", getParserState().buffer);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 截断数据拼接测试通过");
  } else {
    console.log("❌ 截断数据拼接测试失败");
  }
}

/**
 * 测试3: data:前缀截断
 */
function testDataPrefixTruncation() {
  console.log("\n3. 测试data:前缀截断:");
  resetStreamingParser();

  const chunk1 = "da";
  const chunk2 = "ta: ";
  const chunk3 = '{"message": {"content": "Test"}, "event_type": "message"}';

  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);
  console.log("Chunk3:", chunk3);

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1: 解析数量=", result1.length, ", 缓冲区=", getParserState().buffer);

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2: 解析数量=", result2.length, ", 缓冲区=", getParserState().buffer);

  const result3 = parseStreamingData(chunk3);
  console.log("处理chunk3: 解析数量=", result3.length, ", 缓冲区=", getParserState().buffer);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ data:前缀截断测试通过");
  } else {
    console.log("❌ data:前缀截断测试失败");
  }
}

/**
 * 测试4: 简单JSON
 */
function testSimpleJson() {
  console.log("\n4. 测试简单JSON:");
  resetStreamingParser();

  const simpleData = 'data: {"test": "value"}';
  console.log("输入:", simpleData);

  const results = parseStreamingData(simpleData);
  console.log("解析结果数量:", results.length);
  console.log("解析结果:", results);

  if (results.length === 1) {
    console.log("✅ 简单JSON测试通过");
  } else {
    console.log("❌ 简单JSON测试失败");
  }
}

/**
 * 测试5: 多个完整data块
 */
function testMultipleDataBlocks() {
  console.log("\n5. 测试多个完整data块:");
  resetStreamingParser();

  const multiData = `data: {"message": {"content": "消息1"}, "event_type": "message"}
data: {"message": {"content": "消息2"}, "event_type": "message"}`;

  console.log("输入多个data块");

  const results = parseStreamingData(multiData);
  console.log("解析结果数量:", results.length);
  console.log("缓冲区:", getParserState().buffer);

  if (results.length === 2) {
    console.log("✅ 多个data块测试通过");
  } else {
    console.log("❌ 多个data块测试失败");
  }
}

/**
 * 测试6: 混合完整和不完整
 */
function testMixedData() {
  console.log("\n6. 测试混合完整和不完整数据:");
  resetStreamingParser();

  const chunk1 = `data: {"message": {"content": "完整消息"}, "event_type": "message"}
data: {"message": {"content": "不完整`;

  const chunk2 = `消息"}, "event_type": "message"}`;

  console.log("Chunk1: 1个完整 + 1个不完整开始");
  console.log("Chunk2: 1个不完整结束");

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1: 解析数量=", result1.length);

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2: 解析数量=", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 2) {
    console.log("✅ 混合数据测试通过");
  } else {
    console.log("❌ 混合数据测试失败");
  }
}

/**
 * 运行所有测试
 */
export function runFixParsingTests() {
  console.log("开始解析修复测试...");

  testCompleteData();
  testTruncatedData();
  testDataPrefixTruncation();
  testSimpleJson();
  testMultipleDataBlocks();
  testMixedData();

  console.log("\n=== 解析修复测试完成 ===");
}

// 如果直接运行此文件，执行测试
if (typeof window === "undefined") {
  runFixParsingTests();
}
