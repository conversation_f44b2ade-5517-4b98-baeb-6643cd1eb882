/**
 * 测试规范化函数
 */

// 改进的规范化函数
function normalizeToJson(data) {
  let result = data;
  
  // 1. 处理对象属性名（包括嵌套的情况）
  // 匹配：空白字符 + 标识符 + 可选空白 + 冒号
  result = result.replace(/(\s+)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
  
  // 2. 处理对象开始后的第一个属性（没有前导空白的情况）
  result = result.replace(/(\{)\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
  
  // 3. 处理逗号后的属性
  result = result.replace(/(,)\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
  
  return result;
}

// 测试简单的JavaScript对象
function testSimpleObject() {
  console.log("=== 测试简单对象 ===");
  
  const jsObject = `{
  message: {
    id: "123",
    content: "test"
  },
  event_type: "test"
}`;

  console.log("原始对象:");
  console.log(jsObject);
  
  const normalized = normalizeToJson(jsObject);
  console.log("\n规范化后:");
  console.log(normalized);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("\n✅ 解析成功:");
    console.log(parsed);
  } catch (e) {
    console.log("\n❌ 解析失败:", e.message);
  }
}

// 测试复杂的嵌套对象
function testComplexObject() {
  console.log("\n=== 测试复杂对象 ===");
  
  const complexObject = `{
  message: {
    id: "123",
    content: {
      action_content: [
        {
          orderId: "456",
          stockWeight: "100"
        }
      ],
      total: 300
    }
  },
  event_type: "test"
}`;

  console.log("原始复杂对象:");
  console.log(complexObject);
  
  const normalized = normalizeToJson(complexObject);
  console.log("\n规范化后:");
  console.log(normalized);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("\n✅ 复杂对象解析成功:");
    console.log(JSON.stringify(parsed, null, 2));
  } catch (e) {
    console.log("\n❌ 复杂对象解析失败:", e.message);
    
    // 找出问题位置
    const lines = normalized.split('\n');
    lines.forEach((line, index) => {
      console.log(`${index + 1}: ${line}`);
    });
  }
}

// 测试你的实际数据的一小部分
function testRealDataFragment() {
  console.log("\n=== 测试实际数据片段 ===");
  
  const fragment = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    content: {
      action_id: "test",
      total: 300
    }
  },
  event_type: "node_finished"
}`;

  console.log("实际数据片段:");
  console.log(fragment);
  
  const normalized = normalizeToJson(fragment);
  console.log("\n规范化后:");
  console.log(normalized);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("\n✅ 实际数据片段解析成功:");
    console.log(JSON.stringify(parsed, null, 2));
  } catch (e) {
    console.log("\n❌ 实际数据片段解析失败:", e.message);
  }
}

// 运行所有测试
testSimpleObject();
testComplexObject();
testRealDataFragment();
