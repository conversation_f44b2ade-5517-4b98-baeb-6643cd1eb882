/**
 * 调试解析问题
 */

// 完全复制你的规范化函数
function normalizeToJson(data) {
  let result = data;
  
  // 1. 处理对象属性名（包括嵌套的情况）
  result = result.replace(/(\s+)([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 2. 处理对象开始后的第一个属性（没有前导空白的情况）
  result = result.replace(/(\{)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  // 3. 处理逗号后的属性
  result = result.replace(/(,)\s*([a-z_$][\w$]*)\s*:/gi, '$1"$2":');
  
  return result;
}

// 测试你的实际数据的一个完整示例
function testCompleteRealData() {
  console.log("=== 测试完整的真实数据 ===");
  
  // 模拟你的完整数据（合并所有三个chunk）
  const completeData = `{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236", 
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29", 
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西"
        },
        {
          orderId: "4500013082", 
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13"
        }
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed"
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0
  },
  event_type: "node_finished"
}

{
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1", 
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed"
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0
  },
  event_type: "node_finished"
}`;

  console.log("原始数据长度:", completeData.length);
  console.log("原始数据前100字符:", completeData.substring(0, 100));
  
  // 规范化
  const normalized = normalizeToJson(completeData);
  console.log("\n规范化后长度:", normalized.length);
  console.log("规范化后前100字符:", normalized.substring(0, 100));
  
  // 尝试解析第一个JSON对象
  try {
    // 找到第一个完整的JSON对象
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;
    let firstJsonEnd = -1;
    
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized[i];
      
      if (escapeNext) {
        escapeNext = false;
        continue;
      }
      
      if (char === '\\') {
        escapeNext = true;
        continue;
      }
      
      if (char === '"') {
        inString = !inString;
        continue;
      }
      
      if (!inString) {
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
          if (braceCount === 0) {
            firstJsonEnd = i;
            break;
          }
        }
      }
    }
    
    if (firstJsonEnd > -1) {
      const firstJson = normalized.substring(0, firstJsonEnd + 1);
      console.log("\n第一个JSON对象长度:", firstJson.length);
      console.log("第一个JSON对象前100字符:", firstJson.substring(0, 100));
      
      const parsed1 = JSON.parse(firstJson);
      console.log("✅ 第一个JSON解析成功:", parsed1.message.message_id);
      
      // 查找第二个JSON对象
      const remaining = normalized.substring(firstJsonEnd + 1).trim();
      if (remaining.length > 0) {
        console.log("\n剩余数据长度:", remaining.length);
        console.log("剩余数据前100字符:", remaining.substring(0, 100));
        
        if (remaining.startsWith('{')) {
          const parsed2 = JSON.parse(remaining);
          console.log("✅ 第二个JSON解析成功:", parsed2.message.message_id);
          
          console.log("\n🎉 总共成功解析了2个JSON对象");
        } else {
          console.log("❌ 剩余数据不是有效的JSON开始");
        }
      }
    } else {
      console.log("❌ 没有找到完整的JSON对象");
    }
    
  } catch (e) {
    console.log("❌ JSON解析失败:", e.message);
    
    // 显示规范化后的数据以便调试
    console.log("\n规范化后的数据（前500字符）:");
    console.log(normalized.substring(0, 500));
  }
}

// 测试规范化函数的具体问题
function testNormalizationIssues() {
  console.log("\n=== 测试规范化函数的问题 ===");
  
  // 测试一个简单的有问题的例子
  const problematicData = `{
  message: {
    id: "test",
    content: {
      action_content: [
        {
          orderId: "123"
        }
      ]
    }
  }
}`;

  console.log("问题数据:");
  console.log(problematicData);
  
  const normalized = normalizeToJson(problematicData);
  console.log("\n规范化后:");
  console.log(normalized);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("✅ 简单数据解析成功");
  } catch (e) {
    console.log("❌ 简单数据解析失败:", e.message);
  }
}

// 运行测试
testCompleteRealData();
testNormalizationIssues();
