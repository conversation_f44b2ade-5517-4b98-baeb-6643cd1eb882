/**
 * 微信小程序环境测试
 * 模拟真实的微信小程序流式数据接收场景
 */

import { 
  parseStreamingData, 
  resetStreamingParser, 
  hasUnprocessedData, 
  getParserState,
  flushRemainingData 
} from "./useParseStreaming";

/**
 * 模拟微信小程序的 TextDecoder2
 */
class MockTextDecoder2 {
  decode(data: Uint8Array): string {
    // 模拟微信小程序的解码过程
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(data);
  }
}

/**
 * 模拟微信小程序的数据接收场景
 */
function simulateWechatStreamingScenario() {
  console.log("=== 微信小程序流式数据场景测试 ===");
  
  // 重置解析器状态
  resetStreamingParser();
  
  // 模拟你描述的场景：三段截断 + 完整块
  const scenarios = [
    {
      name: "第一次流式输出 - 第一段被截断的内容",
      rawData: 'data: {"message": {"content": "这是一个很长的消息内容，被截断成三部分，第一部分'
    },
    {
      name: "第二次流式输出 - 第二段被截断的内容", 
      rawData: '，这是第二部分的内容，继续描述这个消息'
    },
    {
      name: "第三次流式输出 - 第三段被截断的内容 + 另一个完整的data:块",
      rawData: '，这是第三部分，消息结束", "message_id": "msg1"}, "event_type": "message"}\ndata: {"message": {"content": "这是一个完整的第二条消息", "message_id": "msg2"}, "event_type": "message"}'
    }
  ];

  let totalParsedMessages = 0;
  let allParsedMessages: any[] = [];

  scenarios.forEach((scenario, index) => {
    console.log(`\n>>> ${scenario.name}`);
    console.log(`原始数据: ${scenario.rawData.substring(0, 100)}...`);
    
    // 模拟微信小程序的数据处理流程
    // 1. 将字符串转换为 Uint8Array (模拟网络传输)
    const encoder = new TextEncoder();
    const uint8Array = encoder.encode(scenario.rawData);
    
    // 2. 使用 TextDecoder2 解码 (模拟微信小程序的解码过程)
    const decoder = new MockTextDecoder2();
    const decodeData = decoder.decode(uint8Array);
    
    console.log(`解码后数据: ${decodeData.substring(0, 100)}...`);
    console.log(`解码数据长度: ${decodeData.length}`);
    
    // 3. 调用解析器
    console.log(`调用前缓冲区状态: ${JSON.stringify(getParserState().buffer.substring(0, 50))}...`);
    
    const parseData = parseStreamingData(decodeData);
    
    console.log(`解析结果数量: ${parseData.length}`);
    console.log(`调用后缓冲区状态: ${JSON.stringify(getParserState().buffer.substring(0, 50))}...`);
    console.log(`缓冲区长度: ${getParserState().buffer.length}`);
    
    if (parseData.length > 0) {
      parseData.forEach((item, idx) => {
        console.log(`  消息 ${idx + 1}: ${item.message?.content?.substring(0, 50)}... (ID: ${item.message?.message_id})`);
        allParsedMessages.push(item);
      });
    } else {
      console.log("  本次没有解析出完整消息");
    }
    
    totalParsedMessages += parseData.length;
  });

  // 最后刷新剩余数据
  console.log("\n>>> 刷新剩余数据");
  const remainingData = flushRemainingData();
  console.log(`剩余数据解析结果: ${remainingData.length} 条消息`);
  
  if (remainingData.length > 0) {
    remainingData.forEach((item, idx) => {
      console.log(`  剩余消息 ${idx + 1}: ${item.message?.content?.substring(0, 50)}... (ID: ${item.message?.message_id})`);
      allParsedMessages.push(item);
    });
  }
  
  totalParsedMessages += remainingData.length;

  console.log(`\n=== 测试结果 ===`);
  console.log(`总共解析出 ${totalParsedMessages} 条消息`);
  console.log(`期望解析出 2 条消息`);
  
  console.log("\n所有解析的消息:");
  allParsedMessages.forEach((msg, idx) => {
    console.log(`${idx + 1}. ${msg.message?.content} (ID: ${msg.message?.message_id})`);
  });

  if (totalParsedMessages === 2) {
    console.log("\n✅ 微信小程序流式数据测试通过");
  } else {
    console.log("\n❌ 微信小程序流式数据测试失败");
    console.log("可能的问题:");
    console.log("1. 数据编码/解码问题");
    console.log("2. 解析器状态管理问题");
    console.log("3. 微信小程序环境特殊性");
  }
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log("\n=== 边界情况测试 ===");
  
  // 测试空数据
  resetStreamingParser();
  console.log("\n1. 测试空数据:");
  const emptyResult = parseStreamingData("");
  console.log(`空数据解析结果: ${emptyResult.length} 条消息`);
  
  // 测试只有data:前缀
  resetStreamingParser();
  console.log("\n2. 测试只有data:前缀:");
  const prefixResult = parseStreamingData("data:");
  console.log(`data:前缀解析结果: ${prefixResult.length} 条消息`);
  console.log(`缓冲区状态: ${JSON.stringify(getParserState().buffer)}`);
  
  // 测试无效JSON
  resetStreamingParser();
  console.log("\n3. 测试无效JSON:");
  const invalidResult = parseStreamingData('data: {"invalid": json}');
  console.log(`无效JSON解析结果: ${invalidResult.length} 条消息`);
  
  // 测试中文字符截断
  resetStreamingParser();
  console.log("\n4. 测试中文字符截断:");
  const chineseResult1 = parseStreamingData('data: {"message": {"content": "中文测试消息');
  console.log(`中文截断第一部分: ${chineseResult1.length} 条消息`);
  const chineseResult2 = parseStreamingData('内容", "message_id": "msg1"}, "event_type": "message"}');
  console.log(`中文截断第二部分: ${chineseResult2.length} 条消息`);
  
  const totalChinese = chineseResult1.length + chineseResult2.length;
  console.log(`中文截断总计: ${totalChinese} 条消息`);
}

// 运行测试
simulateWechatStreamingScenario();
testEdgeCases();

export { simulateWechatStreamingScenario, testEdgeCases };
