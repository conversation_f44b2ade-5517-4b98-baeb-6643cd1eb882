/**
 * 复杂截断情况测试
 * 测试各种复杂的数据截断场景，确保解析器能正确处理所有情况
 */

import { 
  parseStreamingData, 
  resetStreamingParser, 
  hasUnprocessedData, 
  getParserState,
  flushRemainingData 
} from "./useParseStreaming";

console.log("=== 复杂截断情况测试 ===");

/**
 * 测试情况1：单个data块被截断为三部分
 * chunk1包含截断的第一部分，chunk2包含截断的第二部分，chunk3包含被截断的第三部分
 */
function testThreePartTruncation() {
  console.log("\n1. 测试三部分截断:");
  resetStreamingParser();

  const completeData = 'data: {"message": {"content": "这是一个很长的消息内容，用来测试截断情况", "message_id": "msg1"}, "event_type": "message"}';
  
  // 将数据分成三部分
  const part1 = 'data: {"message": {"content": "这是一个很长的消息';
  const part2 = '内容，用来测试截断情况", "message_id": "msg1"}, ';
  const part3 = '"event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);
  console.log("Part3:", part3);

  // 处理第一部分
  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length, "缓冲区长度:", getParserState().buffer.length);

  // 处理第二部分
  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length, "缓冲区长度:", getParserState().buffer.length);

  // 处理第三部分
  const result3 = parseStreamingData(part3);
  console.log("处理part3后 - 解析数量:", result3.length, "缓冲区长度:", getParserState().buffer.length);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 三部分截断测试通过");
  } else {
    console.log("❌ 三部分截断测试失败");
  }
}

/**
 * 测试情况2：chunk3包含截断的第三部分 + 一个完整的data块
 */
function testTruncationWithCompleteBlock() {
  console.log("\n2. 测试截断+完整块:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "第一个消息';
  const part2 = '的内容", "message_id": "msg1"}, "event_type": "message"}';
  const part3 = '\ndata: {"message": {"content": "第二个完整消息", "message_id": "msg2"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);
  console.log("Part3:", part3);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const result3 = parseStreamingData(part3);
  console.log("处理part3后 - 解析数量:", result3.length);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 2) {
    console.log("✅ 截断+完整块测试通过");
  } else {
    console.log("❌ 截断+完整块测试失败");
  }
}

/**
 * 测试情况3：chunk3包含截断的第三部分 + 一个不完整的data块
 */
function testTruncationWithIncompleteBlock() {
  console.log("\n3. 测试截断+不完整块:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "第一个消息';
  const part2 = '的内容", "message_id": "msg1"}, "event_type": "message"}';
  const part3 = '\ndata: {"message": {"content": "第二个不完整';

  console.log("Part1:", part1);
  console.log("Part2:", part2);
  console.log("Part3:", part3);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const result3 = parseStreamingData(part3);
  console.log("处理part3后 - 解析数量:", result3.length);

  // 补充剩余数据
  const part4 = '的消息", "message_id": "msg2"}, "event_type": "message"}';
  const result4 = parseStreamingData(part4);
  console.log("处理part4后 - 解析数量:", result4.length);

  const totalResults = result1.length + result2.length + result3.length + result4.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 2) {
    console.log("✅ 截断+不完整块测试通过");
  } else {
    console.log("❌ 截断+不完整块测试失败");
  }
}

/**
 * 测试情况4：data:前缀本身被截断
 */
function testDataPrefixTruncation() {
  console.log("\n4. 测试data:前缀截断:");
  resetStreamingParser();

  const part1 = 'da';
  const part2 = 'ta: {"message": {"content": "测试消息", "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ data:前缀截断测试通过");
  } else {
    console.log("❌ data:前缀截断测试失败");
  }
}

/**
 * 测试情况5：多层嵌套截断
 */
function testNestedTruncation() {
  console.log("\n5. 测试多层嵌套截断:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "消息1", "nested": {"deep": {"value": "深层';
  const part2 = '数据"}, "other": "其他数据"}, "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 多层嵌套截断测试通过");
  } else {
    console.log("❌ 多层嵌套截断测试失败");
  }
}

/**
 * 测试情况6：测试flushRemainingData功能
 */
function testFlushRemainingData() {
  console.log("\n6. 测试刷新剩余数据:");
  resetStreamingParser();

  // 添加一个不完整的数据块
  const incompleteData = 'data: {"message": {"content": "不完整的消息", "message_id": "msg1"}';
  
  console.log("输入不完整数据:", incompleteData);
  
  const result1 = parseStreamingData(incompleteData);
  console.log("正常解析结果数量:", result1.length);
  console.log("缓冲区是否有数据:", hasUnprocessedData());
  
  // 刷新剩余数据
  const flushedResults = flushRemainingData();
  console.log("刷新后解析数量:", flushedResults.length);
  
  if (flushedResults.length > 0) {
    console.log("✅ 刷新剩余数据测试通过");
  } else {
    console.log("❌ 刷新剩余数据测试失败");
  }
}

// 运行所有测试
testThreePartTruncation();
testTruncationWithCompleteBlock();
testTruncationWithIncompleteBlock();
testDataPrefixTruncation();
testNestedTruncation();
testFlushRemainingData();

console.log("\n=== 复杂截断测试完成 ===");
