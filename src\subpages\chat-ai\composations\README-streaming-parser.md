# 流式数据解析器 (Streaming Data Parser)

## 概述

这个流式数据解析器专门用于处理 Server-Sent Events (SSE) 格式的数据流，能够可靠地解析被截断的 JSON 数据块。

## 核心功能

### 1. 数据截断处理

解析器能够处理各种复杂的数据截断情况：

- **单个 JSON 对象被分割成多个 chunk**
- **一个 chunk 包含多个完整和不完整的 data 块**
- **`data:` 前缀本身被截断**
- **JSON 对象在任意位置被截断**

### 2. 支持的截断场景

#### 场景 1: 三部分截断
```
chunk1: 'data: {"message": {"content": "这是一个很长的消息'
chunk2: '内容，用来测试截断情况", "message_id": "msg1"}, '
chunk3: '"event_type": "message"}'
```

#### 场景 2: 截断 + 完整块
```
chunk1: 'data: {"message": {"content": "第一个消息'
chunk2: '的内容", "message_id": "msg1"}, "event_type": "message"}'
chunk3: '\ndata: {"message": {"content": "第二个完整消息", "message_id": "msg2"}, "event_type": "message"}'
```

#### 场景 3: data: 前缀截断
```
chunk1: 'da'
chunk2: 'ta: {"message": {"content": "测试消息", "message_id": "msg1"}, "event_type": "message"}'
```

#### 场景 4: 嵌套对象截断
```
chunk1: 'data: {"message": {"nested": {"level1": {"level2": {"deep'
chunk2: 'Value": "深层数据"}, "other": "其他"}}, "message_id": "msg1"}, "event_type": "message"}'
```

## API 接口

### `parseStreamingData(data: string): ChunkData[]`

主要的解析函数，处理新接收到的数据块。

**参数:**
- `data`: 新接收到的数据块字符串

**返回值:**
- `ChunkData[]`: 解析成功的 JSON 对象数组

### `flushRemainingData(): ChunkData[]`

在流结束时调用，尝试解析缓冲区中剩余的数据。

**返回值:**
- `ChunkData[]`: 从剩余数据中解析出的 JSON 对象数组

### `resetStreamingParser(): void`

重置解析器的内部状态，通常在开始新的流时调用。

### `hasUnprocessedData(): boolean`

检查缓冲区是否还有未处理的数据。

**返回值:**
- `boolean`: 如果有未处理数据返回 true

### `getParserState(): Readonly<StreamParserState>`

获取解析器当前状态信息，用于调试和监控。

## 使用示例

### 基本使用

```typescript
import { parseStreamingData, resetStreamingParser, flushRemainingData } from './useParseStreaming';

// 开始新的流解析
resetStreamingParser();

// 处理接收到的数据块
const chunk1 = 'data: {"message": {"content": "Hello';
const chunk2 = ' World", "message_id": "msg1"}, "event_type": "message"}';

const results1 = parseStreamingData(chunk1); // []
const results2 = parseStreamingData(chunk2); // [ChunkData]

// 流结束时刷新剩余数据
const remainingResults = flushRemainingData(); // []
```

### 在 Adapter 中的使用

```typescript
// 在 streamAdapter 中的使用示例
const listener = ({ data }) => {
  const decodeData = new TextDecoder2().decode(new Uint8Array(data));
  if (!decodeData) return;

  // 使用优化后的解析器
  const parseData: ChunkData[] = parseStreamingData(decodeData);
  parseData.forEach(item => {
    // 处理解析出的数据
    const response: StreamResponse = {
      data: item,
      status: "streaming",
    };
    // 调用转换函数
    config.transformResponse?.(response);
  });
};

// 在流结束时
const complete = (response: any) => {
  // 刷新剩余数据
  const remainingData: ChunkData[] = flushRemainingData();
  remainingData.forEach(item => {
    // 处理剩余数据
  });
  
  resetStreamingParser();
};
```

## 核心算法

### 1. 缓冲区管理

解析器维护一个内部缓冲区，将新接收的数据追加到缓冲区中：

```typescript
parserState.buffer += data;
```

### 2. data: 前缀识别

在缓冲区中查找 `data:` 前缀，支持不完整前缀的保留：

```typescript
const dataIndex = parserState.buffer.indexOf("data:", processedLength);
```

### 3. JSON 边界检测

使用括号计数和字符串状态跟踪来准确识别 JSON 对象的边界：

```typescript
function extractCompleteJsonFromPosition(data, startIndex) {
  let braceCount = 0;
  let inString = false;
  let escapeNext = false;
  // ... 详细的边界检测逻辑
}
```

### 4. 错误恢复

当遇到无效数据时，解析器会跳过错误部分并继续处理后续数据。

## 性能特点

- **内存效率**: 只保留必要的缓冲区数据
- **处理速度**: 单次遍历处理多个数据块
- **错误容忍**: 能够从解析错误中恢复
- **状态管理**: 维护最小必要的状态信息

## 测试覆盖

解析器经过了全面的测试，包括：

- ✅ 基本完整数据解析
- ✅ 三部分截断处理
- ✅ 多个数据块混合截断
- ✅ 转义字符处理
- ✅ 嵌套对象截断
- ✅ 极小数据块处理
- ✅ data: 前缀截断
- ✅ 复杂混合场景

## 注意事项

1. **状态重置**: 每次开始新的流时必须调用 `resetStreamingParser()`
2. **剩余数据**: 流结束时应调用 `flushRemainingData()` 处理剩余数据
3. **错误处理**: 解析器会自动跳过无效的 JSON 数据
4. **内存管理**: 解析器会自动清理已处理的缓冲区数据

## 更新日志

### v1.1.0 (当前版本)
- 添加了 `flushRemainingData()` 函数
- 改进了 JSON 修复机制
- 增强了错误恢复能力
- 完善了测试覆盖

### v1.0.0
- 初始版本
- 基本的截断数据处理
- 核心解析算法实现
