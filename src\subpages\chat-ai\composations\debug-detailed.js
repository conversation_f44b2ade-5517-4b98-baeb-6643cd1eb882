/**
 * 详细调试规范化问题
 */

// 修复后的规范化函数
function normalizeToJson(data) {
  let result = data;
  
  console.log("=== 规范化过程 ===");
  console.log("原始数据长度:", data.length);
  console.log("原始数据前200字符:", data.substring(0, 200));
  
  // 一次性处理所有情况
  result = result.replace(
    /([{\s,\n]+)([a-z_$][\w$]*)\s*:/gi,
    function(match, prefix, propName) {
      console.log(`匹配到: "${match}" -> "${prefix}"${propName}":"`);
      return prefix + '"' + propName + '":';
    }
  );
  
  console.log("规范化后长度:", result.length);
  console.log("规范化后前200字符:", result.substring(0, 200));
  console.log("=== 规范化结束 ===\n");
  
  return result;
}

// 测试一个简单的有问题的例子
function testSimpleCase() {
  console.log("=== 测试简单案例 ===");
  
  const simpleData = `{
  message: {
    id: "test",
    content: {
      action_content: [
        {
          orderId: "123",
          cabinetNo: "456"
        }
      ]
    }
  }
}`;

  console.log("简单数据:");
  console.log(simpleData);
  
  const normalized = normalizeToJson(simpleData);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("✅ 简单案例解析成功");
  } catch (e) {
    console.log("❌ 简单案例解析失败:", e.message);
    
    const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
    console.log("错误位置:", errorPos);
    console.log("错误位置附近的内容:", normalized.substring(errorPos - 20, errorPos + 20));
    
    // 逐字符检查错误位置附近
    console.log("\n逐字符检查:");
    for (let i = Math.max(0, errorPos - 10); i < Math.min(normalized.length, errorPos + 10); i++) {
      const char = normalized[i];
      const marker = i === errorPos ? " <-- ERROR" : "";
      console.log(`${i}: "${char}" (${char.charCodeAt(0)})${marker}`);
    }
  }
}

// 测试你的实际数据的一小部分
function testRealDataFragment() {
  console.log("\n=== 测试实际数据片段 ===");
  
  const fragment = `{
  message: {
    content: {
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965"
        },
        {
          orderId: "4500013082",
          declared: ""
        }
      ]
    }
  }
}`;

  console.log("实际数据片段:");
  console.log(fragment);
  
  const normalized = normalizeToJson(fragment);
  
  try {
    const parsed = JSON.parse(normalized);
    console.log("✅ 实际数据片段解析成功");
  } catch (e) {
    console.log("❌ 实际数据片段解析失败:", e.message);
    
    const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || "0");
    console.log("错误位置:", errorPos);
    console.log("错误位置附近的内容:", normalized.substring(errorPos - 30, errorPos + 30));
  }
}

// 运行测试
testSimpleCase();
testRealDataFragment();
