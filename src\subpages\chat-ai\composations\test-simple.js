/**
 * 简单的JavaScript测试文件
 * 测试复杂截断情况的处理
 */

// 模拟解析器的核心逻辑
let parserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

function resetStreamingParser() {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

function findJsonStart(buffer, startIndex) {
  let index = startIndex;
  
  // 跳过空白字符
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }
  
  // 检查是否找到JSON开始标记
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }
  
  return -1;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

function parseStreamingData(data) {
  // 将新数据追加到缓存中
  parserState.buffer += data;

  const parsedData = [];
  let processedLength = 0;

  // 持续处理缓冲区中的数据
  while (processedLength < parserState.buffer.length) {
    // 从当前位置查找data:
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);

    if (dataIndex === -1) {
      // 没有找到data:，检查剩余数据
      const remaining = parserState.buffer.substring(processedLength);
      const trimmed = remaining.trim();

      // 检查是否是不完整的data:前缀
      if (
        trimmed.length > 0 &&
        trimmed.length < 5 &&
        "data:".startsWith(trimmed)
      ) {
        // 保留不完整的data:前缀
        parserState.buffer = trimmed;
      } else {
        // 没有有效数据，清空缓冲区
        parserState.buffer = "";
      }
      break;
    }

    // 找到data:，查找JSON开始位置
    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);

    if (jsonStart === -1) {
      // 没有找到JSON开始标记
      const remainingLength = parserState.buffer.length - dataIndex;
      if (remainingLength < 20) {
        // 数据太短，可能不完整，保留等待更多数据
        parserState.buffer = parserState.buffer.substring(dataIndex);
        break;
      } else {
        // 跳过这个无效的data:
        processedLength = dataIndex + 5;
        continue;
      }
    }

    // 尝试提取完整的JSON
    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );

    if (jsonResult.jsonStr) {
      // 找到完整的JSON，尝试解析
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed);

          // 成功解析，移除已处理的部分
          const endIndex = jsonStart + jsonResult.jsonStr.length;
          parserState.buffer = parserState.buffer.substring(endIndex);
          processedLength = 0; // 重置处理位置
          continue;
        }
      } catch {
        // JSON解析失败，跳过这个数据块
        processedLength = jsonStart + jsonResult.jsonStr.length;
        continue;
      }
    } else {
      // JSON不完整，保留从dataIndex开始的所有数据
      parserState.buffer = parserState.buffer.substring(dataIndex);
      break;
    }
  }

  return parsedData;
}

// 测试函数
function testThreePartTruncation() {
  console.log("\n1. 测试三部分截断:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "这是一个很长的消息';
  const part2 = '内容，用来测试截断情况", "message_id": "msg1"}, ';
  const part3 = '"event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);
  console.log("Part3:", part3);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length, "缓冲区长度:", parserState.buffer.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length, "缓冲区长度:", parserState.buffer.length);

  const result3 = parseStreamingData(part3);
  console.log("处理part3后 - 解析数量:", result3.length, "缓冲区长度:", parserState.buffer.length);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ 三部分截断测试通过");
    console.log("解析结果:", result3[0]);
  } else {
    console.log("❌ 三部分截断测试失败");
  }
}

function testTruncationWithCompleteBlock() {
  console.log("\n2. 测试截断+完整块:");
  resetStreamingParser();

  const part1 = 'data: {"message": {"content": "第一个消息';
  const part2 = '的内容", "message_id": "msg1"}, "event_type": "message"}';
  const part3 = '\ndata: {"message": {"content": "第二个完整消息", "message_id": "msg2"}, "event_type": "message"}';

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const result3 = parseStreamingData(part3);
  console.log("处理part3后 - 解析数量:", result3.length);

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 2) {
    console.log("✅ 截断+完整块测试通过");
  } else {
    console.log("❌ 截断+完整块测试失败");
  }
}

function testDataPrefixTruncation() {
  console.log("\n3. 测试data:前缀截断:");
  resetStreamingParser();

  const part1 = 'da';
  const part2 = 'ta: {"message": {"content": "测试消息", "message_id": "msg1"}, "event_type": "message"}';

  console.log("Part1:", part1);
  console.log("Part2:", part2);

  const result1 = parseStreamingData(part1);
  console.log("处理part1后 - 解析数量:", result1.length);

  const result2 = parseStreamingData(part2);
  console.log("处理part2后 - 解析数量:", result2.length);

  const totalResults = result1.length + result2.length;
  console.log("总解析数量:", totalResults);

  if (totalResults === 1) {
    console.log("✅ data:前缀截断测试通过");
  } else {
    console.log("❌ data:前缀截断测试失败");
  }
}

// 运行测试
console.log("=== 复杂截断情况测试 ===");
testThreePartTruncation();
testTruncationWithCompleteBlock();
testDataPrefixTruncation();
console.log("\n=== 测试完成 ===");
