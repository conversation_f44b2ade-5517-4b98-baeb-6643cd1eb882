/**
 * 测试特定问题：三段截断 + 完整块
 */

// 模拟解析器核心逻辑
let parserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

function resetStreamingParser() {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}

function findJsonStart(buffer, startIndex) {
  let index = startIndex;
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }
  return -1;
}

function extractCompleteJsonFromPosition(data, startIndex) {
  if (startIndex >= data.length || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < data.length; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isComplete: true,
          };
        }
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  return { jsonStr: null, isComplete: false };
}

function parseStreamingData(data) {
  console.log("=== parseStreamingData 调用 ===");
  console.log("输入数据:", JSON.stringify(data));
  console.log("当前缓冲区:", JSON.stringify(parserState.buffer));
  
  // 将新数据追加到缓存中
  parserState.buffer += data;
  console.log("追加后缓冲区:", JSON.stringify(parserState.buffer));

  const parsedData = [];
  let processedLength = 0;

  while (processedLength < parserState.buffer.length) {
    console.log(`处理位置: ${processedLength}, 缓冲区长度: ${parserState.buffer.length}`);
    
    // 从当前位置查找data:
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);
    console.log(`查找data:结果: ${dataIndex}`);

    if (dataIndex === -1) {
      // 没有找到data:，检查剩余数据
      const remaining = parserState.buffer.substring(processedLength);
      const trimmed = remaining.trim();
      console.log("没有找到data:, 剩余数据:", JSON.stringify(trimmed));

      // 检查是否是不完整的data:前缀
      if (
        trimmed.length > 0 &&
        trimmed.length < 5 &&
        "data:".startsWith(trimmed)
      ) {
        // 保留不完整的data:前缀
        parserState.buffer = trimmed;
        console.log("保留不完整的data:前缀:", JSON.stringify(parserState.buffer));
      } else {
        // 没有有效数据，清空缓冲区
        parserState.buffer = "";
        console.log("清空缓冲区");
      }
      break;
    }

    // 找到data:，查找JSON开始位置
    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);
    console.log(`JSON开始位置: ${jsonStart}`);

    if (jsonStart === -1) {
      // 没有找到JSON开始标记
      const remainingLength = parserState.buffer.length - dataIndex;
      console.log(`没有找到JSON开始标记, 剩余长度: ${remainingLength}`);
      
      if (remainingLength < 20) {
        // 数据太短，可能不完整，保留等待更多数据
        parserState.buffer = parserState.buffer.substring(dataIndex);
        console.log("数据太短，保留等待更多数据:", JSON.stringify(parserState.buffer));
        break;
      } else {
        // 跳过这个无效的data:
        processedLength = dataIndex + 5;
        console.log("跳过无效的data:, 新处理位置:", processedLength);
        continue;
      }
    }

    // 尝试提取完整的JSON
    const jsonResult = extractCompleteJsonFromPosition(
      parserState.buffer,
      jsonStart,
    );
    console.log("JSON提取结果:", jsonResult.jsonStr ? "找到完整JSON" : "JSON不完整");

    if (jsonResult.jsonStr) {
      // 找到完整的JSON，尝试解析
      try {
        const parsed = JSON.parse(jsonResult.jsonStr);
        if (parsed && typeof parsed === "object") {
          parsedData.push(parsed);
          console.log("成功解析JSON:", parsed.message?.message_id || "unknown");

          // 成功解析，移除已处理的部分
          const endIndex = jsonStart + jsonResult.jsonStr.length;
          parserState.buffer = parserState.buffer.substring(endIndex);
          console.log("移除已处理部分，新缓冲区:", JSON.stringify(parserState.buffer));
          processedLength = 0; // 重置处理位置
          continue;
        }
      } catch (e) {
        // JSON解析失败，跳过这个数据块
        console.log("JSON解析失败:", e.message);
        processedLength = jsonStart + jsonResult.jsonStr.length;
        continue;
      }
    } else {
      // JSON不完整，保留从dataIndex开始的所有数据
      parserState.buffer = parserState.buffer.substring(dataIndex);
      console.log("JSON不完整，保留数据:", JSON.stringify(parserState.buffer));
      break;
    }
  }

  console.log("parsedData:", parsedData);
  console.log("最终缓冲区:", JSON.stringify(parserState.buffer));
  console.log("=== parseStreamingData 结束 ===\n");
  
  return parsedData;
}

// 测试你描述的具体场景
function testSpecificIssue() {
  console.log("=== 测试特定问题场景 ===");
  resetStreamingParser();

  // 模拟你描述的场景
  const chunk1 = 'data: {"message": {"content": "这是一个很长的消息内容，被截断成三部分，第一部分';
  const chunk2 = '，这是第二部分的内容，继续描述这个消息';
  const chunk3 = '，这是第三部分，消息结束", "message_id": "msg1"}, "event_type": "message"}\ndata: {"message": {"content": "这是一个完整的第二条消息", "message_id": "msg2"}, "event_type": "message"}';

  console.log("Chunk 1:", JSON.stringify(chunk1));
  console.log("Chunk 2:", JSON.stringify(chunk2));
  console.log("Chunk 3:", JSON.stringify(chunk3));
  console.log("");

  // 第一次流式输出
  console.log(">>> 处理第一次流式输出");
  const result1 = parseStreamingData(chunk1);
  console.log(`第一次结果: ${result1.length} 条消息`);

  // 第二次流式输出
  console.log(">>> 处理第二次流式输出");
  const result2 = parseStreamingData(chunk2);
  console.log(`第二次结果: ${result2.length} 条消息`);

  // 第三次流式输出
  console.log(">>> 处理第三次流式输出");
  const result3 = parseStreamingData(chunk3);
  console.log(`第三次结果: ${result3.length} 条消息`);

  const totalResults = result1.length + result2.length + result3.length;
  console.log(`\n总共解析出 ${totalResults} 条消息`);

  // 显示所有解析的消息
  const allResults = [...result1, ...result2, ...result3];
  allResults.forEach((result, index) => {
    console.log(`消息 ${index + 1}: ${result.message.content.substring(0, 50)}... (ID: ${result.message.message_id})`);
  });

  if (totalResults === 2) {
    console.log("✅ 测试通过：正确解析了2条消息");
  } else {
    console.log("❌ 测试失败：期望2条消息，实际", totalResults, "条");
  }
}

// 运行测试
testSpecificIssue();
